import { describe, it, expect, beforeAll, afterAll, beforeEach } from '@jest/globals';
import { FastifyInstance } from 'fastify';
import {
  prisma,
  setupTestDatabase,
  createTestUser,
  createTestWebhook,
  createTestDomain,
  createTestFastifyApp
} from '../../setup/test-db-setup.js';

describe('Webhook-Alias API Integration', () => {
  setupTestDatabase();

  let app: FastifyInstance;
  let testUser: any;
  let testDomain: any;
  let testWebhook: any;

  beforeAll(async () => {
    app = await createTestFastifyApp();

    // Register the webhook-alias routes
    await app.register(async function (fastify) {
      await fastify.register((await import('../../../src/backend/routes/user-webhook-alias.routes.js')).webhookAliasRoutes);
    });

    await app.ready();
  });

  afterAll(async () => {
    await app?.close();
  });

  beforeEach(async () => {
    // Create test user, webhook, and domain
    testUser = await createTestUser({
      email: '<EMAIL>',
      password: 'testpassword123'
    });

    testWebhook = await createTestWebhook(testUser.id, {
      name: 'Test Webhook',
      url: 'https://test.example.com/webhook',
      verified: true
    });

    testDomain = await createTestDomain(testUser.id, testWebhook.id, {
      domain: 'test-webhook-alias.com',
      verified: true
    });
  });

  describe('Webhook-Alias Service Integration', () => {
    it('should create catch-all alias with webhook using service', async () => {
      const { WebhookAliasService } = await import('../../../src/backend/services/user/webhook-alias.service.js');
      const service = new WebhookAliasService();

      const result = await service.createWebhookAlias({
        domainId: testDomain.id,
        webhookUrl: 'https://hooks.n8n.cloud/webhook/test-catchall',
        webhookName: 'Test Catch-All Handler',
        webhookDescription: 'Test webhook for catch-all alias',
        aliasType: 'catchall',
        syncWithDomain: true,
        autoVerify: false,
        userId: testUser.id
      });

      expect(result.success).toBe(true);
      expect(result.webhook).toBeDefined();
      expect(result.webhook!.url).toBe('https://hooks.n8n.cloud/webhook/test-catchall');
      expect(result.webhook!.name).toBe('Test Catch-All Handler');
      expect(result.webhook!.verified).toBe(false);
      expect(result.webhook!.verificationToken).toBeDefined();

      expect(result.alias).toBeDefined();
      expect(result.alias!.email).toBe(`*@${testDomain.domain}`);
      expect(result.alias!.active).toBe(false); // Should be false since webhook is unverified

      expect(result.domain).toBeDefined();
      expect(result.domain!.webhookUpdated).toBe(true);
      expect(result.message).toContain('catch-all alias');

      // Verify database state
      const createdWebhook = await prisma.webhook.findUnique({
        where: { id: result.webhook!.id }
      });
      expect(createdWebhook).toBeTruthy();
      expect(createdWebhook!.url).toBe('https://hooks.n8n.cloud/webhook/test-catchall');

      const createdAlias = await prisma.alias.findUnique({
        where: { id: result.alias!.id }
      });
      expect(createdAlias).toBeTruthy();
      expect(createdAlias!.email).toBe(`*@${testDomain.domain}`);

      const updatedDomain = await prisma.domain.findUnique({
        where: { id: testDomain.id }
      });
      expect(updatedDomain!.webhookId).toBe(result.webhook!.id);
    });

    it('should create specific alias with webhook using service', async () => {
      const { WebhookAliasService } = await import('../../../src/backend/services/user/webhook-alias.service.js');
      const service = new WebhookAliasService();

      const result = await service.createWebhookAlias({
        domainId: testDomain.id,
        webhookUrl: 'https://hooks.n8n.cloud/webhook/test-support',
        webhookName: 'Support Email Handler',
        webhookDescription: 'Test webhook for support emails',
        aliasType: 'specific',
        localPart: 'support',
        autoVerify: false,
        userId: testUser.id
      });

      expect(result.success).toBe(true);
      expect(result.webhook).toBeDefined();
      expect(result.webhook!.url).toBe('https://hooks.n8n.cloud/webhook/test-support');
      expect(result.webhook!.name).toBe('Support Email Handler');

      expect(result.alias).toBeDefined();
      expect(result.alias!.email).toBe(`support@${testDomain.domain}`);

      expect(result.domain).toBeUndefined(); // No domain sync for specific aliases
      expect(result.message).toContain('support@');

      // Verify database state
      const createdAlias = await prisma.alias.findUnique({
        where: { id: result.alias!.id }
      });
      expect(createdAlias).toBeTruthy();
      expect(createdAlias!.email).toBe(`support@${testDomain.domain}`);
    });

    it('should reject request without required fields', async () => {
      const { WebhookAliasService } = await import('../../../src/backend/services/user/webhook-alias.service.js');
      const service = new WebhookAliasService();

      const validation = service.validateCreateRequest({
        domainId: testDomain.id,
        webhookUrl: 'https://hooks.n8n.cloud/webhook/test',
        // Missing webhookName and aliasType
      });

      expect(validation.valid).toBe(false);
      expect(validation.error).toContain('required');
    });

    it('should reject specific alias without localPart', async () => {
      const { WebhookAliasService } = await import('../../../src/backend/services/user/webhook-alias.service.js');
      const service = new WebhookAliasService();

      const validation = service.validateCreateRequest({
        domainId: testDomain.id,
        webhookUrl: 'https://hooks.n8n.cloud/webhook/test',
        webhookName: 'Test Handler',
        aliasType: 'specific'
        // Missing localPart
      });

      expect(validation.valid).toBe(false);
      expect(validation.error).toContain('localPart is required');
    });

    it('should reject request for non-existent domain', async () => {
      const { WebhookAliasService } = await import('../../../src/backend/services/user/webhook-alias.service.js');
      const service = new WebhookAliasService();

      const result = await service.createWebhookAlias({
        domainId: 'non-existent-domain-id',
        webhookUrl: 'https://hooks.n8n.cloud/webhook/test',
        webhookName: 'Test Handler',
        aliasType: 'catchall',
        userId: testUser.id
      });

      expect(result.success).toBe(false);
      expect(result.error).toContain('not found');
    });

    it('should reject duplicate alias creation', async () => {
      const { WebhookAliasService } = await import('../../../src/backend/services/user/webhook-alias.service.js');
      const service = new WebhookAliasService();

      // First create an alias
      const firstResult = await service.createWebhookAlias({
        domainId: testDomain.id,
        webhookUrl: 'https://hooks.n8n.cloud/webhook/first',
        webhookName: 'First Handler',
        aliasType: 'catchall',
        userId: testUser.id
      });

      expect(firstResult.success).toBe(true);

      // Try to create the same alias again
      const secondResult = await service.createWebhookAlias({
        domainId: testDomain.id,
        webhookUrl: 'https://hooks.n8n.cloud/webhook/second',
        webhookName: 'Second Handler',
        aliasType: 'catchall',
        userId: testUser.id
      });

      expect(secondResult.success).toBe(false);
      expect(secondResult.error).toContain('already exists');
    });

    it('should validate webhook URL format', async () => {
      const { WebhookAliasService } = await import('../../../src/backend/services/user/webhook-alias.service.js');
      const service = new WebhookAliasService();

      const validation = service.validateCreateRequest({
        domainId: testDomain.id,
        webhookUrl: 'not-a-valid-url',
        webhookName: 'Test Handler',
        aliasType: 'catchall'
      });

      expect(validation.valid).toBe(false);
      expect(validation.error).toContain('valid URL');
    });
  });
});
